@echo off
title Nika Windows Build

echo ========================================
echo    Nika Windows Build Script
echo ========================================
echo.

REM Get the directory where this script is located
set SCRIPT_DIR=%~dp0
echo Script location: %SCRIPT_DIR%

REM Change to the script directory
cd /d "%SCRIPT_DIR%"
echo Current directory: %CD%
echo.

REM Check for required files
echo Checking for required files...
if not exist "CMakeLists.txt" (
    echo [ERROR] CMakeLists.txt not found!
    echo Please make sure you're in the correct project directory.
    echo.
    echo Expected files:
    echo - CMakeLists.txt
    echo - Nika.cpp
    echo - includes.hpp
    echo.
    pause
    exit /b 1
)

if not exist "Nika.cpp" (
    echo [ERROR] Nika.cpp not found!
    pause
    exit /b 1
)

echo [OK] Required files found!
echo.

REM Check for CMake
echo Checking for CMake...
cmake --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] CMake not found!
    echo Please install CMake and add it to your PATH.
    echo Download from: https://cmake.org/download/
    pause
    exit /b 1
)
echo [OK] CMake found!
echo.

REM Clean previous build
if exist "build" (
    echo Cleaning previous build...
    rmdir /s /q "build"
)

REM Create build directory
echo Creating build directory...
mkdir build
cd build

REM Check for vcpkg
set CMAKE_ARGS=-G "Visual Studio 17 2022" -A x64
if exist "..\vcpkg\scripts\buildsystems\vcpkg.cmake" (
    echo [INFO] vcpkg found, using vcpkg toolchain...
    set CMAKE_ARGS=%CMAKE_ARGS% -DCMAKE_TOOLCHAIN_FILE=..\vcpkg\scripts\buildsystems\vcpkg.cmake
) else (
    echo [WARNING] vcpkg not found. If build fails, run install_dependencies.bat first.
)

REM Configure project
echo.
echo ========================================
echo    Configuring Project
echo ========================================
cmake .. %CMAKE_ARGS%

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [ERROR] CMake configuration failed!
    echo.
    echo Possible solutions:
    echo 1. Install dependencies: run install_dependencies.bat
    echo 2. Install Visual Studio 2022 with C++ development tools
    echo 3. Install CMake and add to PATH
    echo.
    echo If GLFW3 is missing, run: install_dependencies.bat
    echo.
    pause
    exit /b 1
)

REM Build project
echo.
echo ========================================
echo    Building Project
echo ========================================
cmake --build . --config Release

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [ERROR] Build failed!
    echo Check the error messages above for details.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Build Successful!
echo ========================================
echo.
echo Executable location: %CD%\Release\main.exe
echo.
echo To run Nika:
echo 1. Start Apex Legends
echo 2. Run as Administrator: .\Release\main.exe
echo.
echo Controls:
echo - Left Arrow: Toggle aimbot left lock
echo - Right Arrow: Toggle ADS locking
echo - Up Arrow: Toggle triggerbot auto fire
echo.
pause
