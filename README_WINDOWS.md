# Nika - Windows 11 Build Instructions

This is a Windows 11 port of the Nika Linux cheat for Apex Legends.

## Prerequisites

### 1. Install Required Software

- **Visual Studio 2022** (Community Edition is fine)
  - Make sure to install "Desktop development with C++" workload
  - Include CMake tools for Visual Studio

- **Git** (if not already installed)

### 2. Install Dependencies

#### Option A: Using vcpkg (Recommended)
```powershell
# Clone vcpkg
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat

# Install dependencies
.\vcpkg install glfw3:x64-windows
.\vcpkg install opengl:x64-windows

# Integrate with Visual Studio
.\vcpkg integrate install
```

#### Option B: Manual Installation
- Download and install GLFW from: https://www.glfw.org/download.html
- Make sure OpenGL is available (usually comes with graphics drivers)

### 3. Build Instructions

#### Using PowerShell (Recommended)
```powershell
# Run as Administrator (required for memory operations)
.\build_windows.ps1
```

#### Using Command Prompt
```cmd
REM Run as Administrator
build_windows.bat
```

#### Manual CMake Build
```powershell
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

### 4. Running the Application

1. **Run as Administrator** (required for memory access)
2. Start Apex Legends
3. Run the executable:
   ```cmd
   .\build\Release\main.exe
   ```

## Key Differences from Linux Version

- **No sudo required** - Run as Administrator instead
- **Windows API** - Uses Windows API for input simulation and memory access
- **Process detection** - Uses Windows process enumeration instead of pidof
- **Key mapping** - Converts X11 key names to Windows virtual key codes

## Controls

- **Left Arrow**: Toggle left lock
- **Right Arrow**: Toggle right lock  
- **Up Arrow**: Toggle auto fire
- **P**: Print player levels (if enabled)
- **M**: Map radar toggle (if enabled)

## Troubleshooting

### Build Issues
- Make sure Visual Studio 2022 is installed with C++ tools
- Verify CMake is in your PATH
- Check that vcpkg dependencies are properly installed

### Runtime Issues
- **"OPEN APEX LEGENDS!"** - Start Apex Legends before running the cheat
- **Access denied errors** - Make sure to run as Administrator
- **DLL errors** - Ensure all dependencies are properly installed

### Performance
- Set Apex Legends to "Borderless Window" mode
- Disable Windows Game Mode if experiencing issues
- Close unnecessary background applications

## Notes

- This port maintains compatibility with the original Linux version
- All features from the Linux version should work on Windows
- The overlay system uses GLFW which works cross-platform
- Memory reading/writing uses Windows API instead of Linux process_vm_readv

## Credits

- Original Linux version by Pesci-Apu
- Overlay implementation from Gerosity/zap-client
- Windows port modifications for cross-platform compatibility
