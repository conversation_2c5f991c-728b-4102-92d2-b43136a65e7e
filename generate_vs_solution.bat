@echo off
echo Generating Visual Studio Solution...

REM Create build directory
if not exist "vs_build" mkdir vs_build
cd vs_build

REM Generate Visual Studio solution
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Visual Studio solution generated successfully!
    echo Open: vs_build\main.sln
    echo.
    echo Would you like to open the solution now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        start main.sln
    )
) else (
    echo.
    echo Failed to generate Visual Studio solution!
    echo Make sure CMake and Visual Studio 2022 are installed.
)

pause
