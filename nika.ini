# ENABLE/DISABLE FEATURES
FEATURE_AIMBOT_ON                      YES #[YES,NO] Does your aim suck?
FEATURE_SENSE_ON                       NO #[YES,NO] Do you have zero awareness?
FEATURE_ITEM_GLOW_ON                   NO #[YES,NO] Loba style glow
FEATURE_NORECOIL_ON                    NO #[YES,NO] Want weapon kick reduction?
FEATURE_TRIGGERBOT_ON                  YES #[YES,NO] Do you pull instead of squeeze?
FEATURE_QUICKTURN_ON                   NO #[YES,NO] Are you paranoid?
FEATURE_QUICKTURN_BUTTON               XK_F #[Check key_codes.txt] Turn 180° when key's held down

FEATURE_SPECTATOR_ON                   YES #[YES,NO]
FEATURE_SKINCHANGER_ON                 NO #[YES,NO]
FEATURE_PRINT_LEVELS_ON                NO #[YES,NO]
FEATURE_PRINT_LEVELS_BUTTON            XK_P #[Activates in game] Print Player levels and names (not 100% accurate)
FEATURE_SUPER_GLIDE_ON                 NO #[YES,NO]
FEATURE_MAP_RADAR_ON                   NO #[YES,NO] **Don't activate while looking at map** Press print_levels_button to activate

FEATURE_MAP_RADAR_BUTTON               XK_M #[Activates in game] Activate big map radar *make sure the key to open map in game isn't the same as this*

#NORECOIL
NORECOIL_PITCH_REDUCTION               12 #[0-99999] The higher the more reduction. Vertical Recoil
NORECOIL_YAW_REDUCTION                 10 #[0-99999] The higher the more reduction. Horizontal Recoil

#TRIGGERBOT
TRIGGERBOT_ZOOMED_RANGE                180 #[0-99999] Max range trigger bot will work at when firing gun while zooming with a weapon.
TRIGGERBOT_HIPFIRE_RANGE               25 #[0-99999] Max range trigger bot will work at when firing gun from hip.
TRIGGERBOT_PAUSE_BUTTON                XK_Z #[Check key_codes.txt] Triggerbot will not work while this keyboard key is held.

#SENSE
SENSE_MAXRANGE                         250 #[0-99999] Sense will not work if the target is too far. Units are meters.

# AIMBOT
AIMBOT_ACTIVATED_BY_ATTACK             YES #[YES,NO] Aimbot will be activated when shooting
AIMBOT_ACTIVATED_BY_ADS                YES #[YES,NO] Aimbot will activate when zooming with a weapon
AIMBOT_ACTIVATED_BY_KEY                YES #[YES,NO] Aimbot will activate when pressing the key below.
AIMBOT_ACTIVATION_KEY                  XK_Shift_L #[Check key_codes.txt else leave empty or put NONE] Aimbot will be activated when this key is pressed.

AIMBOT_SMOOTH                          6 #[5-9999] Smaller = Faster
AIMBOT_SPEED                           90 #[1-9999] Bigger = Faster
AIMBOT_SMOOTH_EXTRA_BY_DISTANCE        3000 #[0-9999] The closer the enemy the more smoothing
AIMBOT_FOV                             15 #[0.0000-180.0000] How close to the crosshairs will the aimbot activate

AIMBOT_PREDICT_BULLETDROP              YES #[YES,NO] Self explanatory
AIMBOT_PREDICT_MOVEMENT                YES #[YES,NO] Self explanatory
AIMBOT_ALLOW_TARGET_SWITCH             YES #[YES,NO] Self explanatory

AIMBOT_MAX_DISTANCE                    150 #[0-9999] Aimbot will not work if the target is too far. Units are meters.
AIMBOT_MIN_DISTANCE                    0 #[0-9999] Aimbot will not work if the target is too close. Units are meters.
AIMBOT_ZOOMED_MAX_MOVE                 15
AIMBOT_HIPFIRE_MAX_MOVE                30
