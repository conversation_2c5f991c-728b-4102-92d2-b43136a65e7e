# Nika Windows Setup Script
# Run this script as Administrator

param(
    [switch]$SkipVcpkg,
    [switch]$SkipBuild
)

Write-Host "=== Nika Windows Setup ===" -ForegroundColor Green
Write-Host ""

# Check if running as Administrator
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "WARNING: Not running as Administrator!" -ForegroundColor Yellow
    Write-Host "Some features may not work properly." -ForegroundColor Yellow
    Write-Host ""
}

# Check for Visual Studio
Write-Host "Checking for Visual Studio 2022..." -ForegroundColor Yellow
$vsPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\*\Common7\IDE\devenv.exe"
if (-not (Test-Path $vsPath)) {
    Write-Host "Visual Studio 2022 not found!" -ForegroundColor Red
    Write-Host "Please install Visual Studio 2022 with C++ development tools." -ForegroundColor Red
    Write-Host "Download from: https://visualstudio.microsoft.com/downloads/" -ForegroundColor Cyan
    exit 1
}
Write-Host "Visual Studio 2022 found!" -ForegroundColor Green

# Check for CMake
Write-Host "Checking for CMake..." -ForegroundColor Yellow
try {
    $cmakeVersion = cmake --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "CMake found!" -ForegroundColor Green
    } else {
        throw "CMake not found"
    }
} catch {
    Write-Host "CMake not found!" -ForegroundColor Red
    Write-Host "Please install CMake or make sure it's in your PATH." -ForegroundColor Red
    Write-Host "Download from: https://cmake.org/download/" -ForegroundColor Cyan
    exit 1
}

# Setup vcpkg if not skipped
if (-not $SkipVcpkg) {
    Write-Host ""
    Write-Host "Setting up vcpkg dependencies..." -ForegroundColor Yellow
    
    if (-not (Test-Path "vcpkg")) {
        Write-Host "Cloning vcpkg..." -ForegroundColor Yellow
        git clone https://github.com/Microsoft/vcpkg.git
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Failed to clone vcpkg!" -ForegroundColor Red
            exit 1
        }
    }
    
    Set-Location "vcpkg"
    
    if (-not (Test-Path "vcpkg.exe")) {
        Write-Host "Bootstrapping vcpkg..." -ForegroundColor Yellow
        .\bootstrap-vcpkg.bat
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Failed to bootstrap vcpkg!" -ForegroundColor Red
            exit 1
        }
    }
    
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    .\vcpkg install glfw3:x64-windows
    .\vcpkg install opengl:x64-windows
    
    Write-Host "Integrating with Visual Studio..." -ForegroundColor Yellow
    .\vcpkg integrate install
    
    Set-Location ".."
    Write-Host "vcpkg setup complete!" -ForegroundColor Green
}

# Build project if not skipped
if (-not $SkipBuild) {
    Write-Host ""
    Write-Host "Building project..." -ForegroundColor Yellow
    
    if (Test-Path "build") {
        Remove-Item -Recurse -Force "build"
    }
    
    New-Item -ItemType Directory -Path "build"
    Set-Location "build"
    
    # Configure with vcpkg toolchain if available
    $cmakeArgs = @("..", "-G", "Visual Studio 17 2022", "-A", "x64")
    if (Test-Path "..\vcpkg\scripts\buildsystems\vcpkg.cmake") {
        $cmakeArgs += "-DCMAKE_TOOLCHAIN_FILE=..\vcpkg\scripts\buildsystems\vcpkg.cmake"
    }
    
    cmake @cmakeArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Host "CMake configuration failed!" -ForegroundColor Red
        exit 1
    }
    
    cmake --build . --config Release
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed!" -ForegroundColor Red
        exit 1
    }
    
    Set-Location ".."
    Write-Host "Build complete!" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== Setup Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "To run Nika:" -ForegroundColor Cyan
Write-Host "1. Start Apex Legends" -ForegroundColor White
Write-Host "2. Run as Administrator: .\build\Release\main.exe" -ForegroundColor White
Write-Host ""
Write-Host "Controls:" -ForegroundColor Cyan
Write-Host "- Left Arrow: Toggle left lock" -ForegroundColor White
Write-Host "- Right Arrow: Toggle right lock" -ForegroundColor White
Write-Host "- Up Arrow: Toggle auto fire" -ForegroundColor White
