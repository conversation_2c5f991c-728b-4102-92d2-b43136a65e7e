
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.12.12+1cce77968 for .NET Framework
      Build started 6/2/2025 3:35:36 AM.
      
      Project "C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\4.0.1\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        D:\\porgramcode\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        D:\\porgramcode\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\4.0.1\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=D:\\porgramcode\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.42.34433\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\4.0.1\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.67
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/4.0.1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.12.12+1cce77968 for .NET Framework
      Build started 6/2/2025 3:35:37 AM.
      
      Project "C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\4.0.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        D:\\porgramcode\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        D:\\porgramcode\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\4.0.1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=D:\\porgramcode\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.42.34433\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\4.0.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.70
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/4.0.1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/CMakeScratch/TryCompile-g1dagg"
      binary: "C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/CMakeScratch/TryCompile-g1dagg"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/CMakeScratch/TryCompile-g1dagg'
        
        Run Build Command(s): "D:/porgramcode/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_ff177.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.12.12+1cce77968 for .NET Framework
        Build started 6/2/2025 3:35:38 AM.
        
        Project "C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\CMakeScratch\\TryCompile-g1dagg\\cmTC_ff177.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_ff177.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\CMakeScratch\\TryCompile-g1dagg\\Debug\\".
          Creating directory "cmTC_ff177.dir\\Debug\\cmTC_ff177.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_ff177.dir\\Debug\\cmTC_ff177.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_ff177.dir\\Debug\\cmTC_ff177.tlog\\unsuccessfulbuild".
        ClCompile:
          D:\\porgramcode\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ff177.dir\\Debug\\\\" /Fd"cmTC_ff177.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34436 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ff177.dir\\Debug\\\\" /Fd"cmTC_ff177.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          D:\\porgramcode\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\CMakeScratch\\TryCompile-g1dagg\\Debug\\cmTC_ff177.exe" /INCREMENTAL /ILK:"cmTC_ff177.dir\\Debug\\cmTC_ff177.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/CMakeScratch/TryCompile-g1dagg/Debug/cmTC_ff177.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/CMakeScratch/TryCompile-g1dagg/Debug/cmTC_ff177.lib" /MACHINE:X64  /machine:x64 cmTC_ff177.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_ff177.vcxproj -> C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\CMakeScratch\\TryCompile-g1dagg\\Debug\\cmTC_ff177.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_ff177.dir\\Debug\\cmTC_ff177.tlog\\unsuccessfulbuild".
          Touching "cmTC_ff177.dir\\Debug\\cmTC_ff177.tlog\\cmTC_ff177.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\CMakeScratch\\TryCompile-g1dagg\\cmTC_ff177.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': D:/porgramcode/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.42.34433/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "D:/porgramcode/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.42.34433/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.42.34436.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/CMakeScratch/TryCompile-bkbc6w"
      binary: "C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/CMakeScratch/TryCompile-bkbc6w"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/CMakeScratch/TryCompile-bkbc6w'
        
        Run Build Command(s): "D:/porgramcode/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c64a8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.12.12+1cce77968 for .NET Framework
        Build started 6/2/2025 3:35:39 AM.
        
        Project "C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\CMakeScratch\\TryCompile-bkbc6w\\cmTC_c64a8.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_c64a8.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\CMakeScratch\\TryCompile-bkbc6w\\Debug\\".
          Creating directory "cmTC_c64a8.dir\\Debug\\cmTC_c64a8.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_c64a8.dir\\Debug\\cmTC_c64a8.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_c64a8.dir\\Debug\\cmTC_c64a8.tlog\\unsuccessfulbuild".
        ClCompile:
          D:\\porgramcode\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_c64a8.dir\\Debug\\\\" /Fd"cmTC_c64a8.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34436 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_c64a8.dir\\Debug\\\\" /Fd"cmTC_c64a8.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          D:\\porgramcode\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\CMakeScratch\\TryCompile-bkbc6w\\Debug\\cmTC_c64a8.exe" /INCREMENTAL /ILK:"cmTC_c64a8.dir\\Debug\\cmTC_c64a8.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/CMakeScratch/TryCompile-bkbc6w/Debug/cmTC_c64a8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Downloads/Nika-Read-Only-main_[unknowncheats.me]_/Nika-Read-Only-main/vs_build/CMakeFiles/CMakeScratch/TryCompile-bkbc6w/Debug/cmTC_c64a8.lib" /MACHINE:X64  /machine:x64 cmTC_c64a8.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_c64a8.vcxproj -> C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\CMakeScratch\\TryCompile-bkbc6w\\Debug\\cmTC_c64a8.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_c64a8.dir\\Debug\\cmTC_c64a8.tlog\\unsuccessfulbuild".
          Touching "cmTC_c64a8.dir\\Debug\\cmTC_c64a8.tlog\\cmTC_c64a8.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Downloads\\Nika-Read-Only-main_[unknowncheats.me]_\\Nika-Read-Only-main\\vs_build\\CMakeFiles\\CMakeScratch\\TryCompile-bkbc6w\\cmTC_c64a8.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.65
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': D:/porgramcode/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.42.34433/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "D:/porgramcode/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.42.34433/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.42.34436.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
