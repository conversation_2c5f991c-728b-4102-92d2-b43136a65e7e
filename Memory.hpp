#pragma once
namespace mem {
#ifdef _WIN32
    DWORD m_pid = 0;
    HANDLE m_process = NULL;

    DWORD GetPID() {
        if (m_pid > 0) return m_pid;

        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) return 0;

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                if (strcmp(pe32.szExeFile, "r5apex.exe") == 0) {
                    m_pid = pe32.th32ProcessID;
                    CloseHandle(hSnapshot);

                    // Open process handle for memory operations
                    m_process = OpenProcess(PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION, FALSE, m_pid);
                    return m_pid;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
        return 0;
    }
#else
    pid_t m_pid = 0;

    pid_t GetPID() {
        if (m_pid > 0) return m_pid;
        char buf[512];
        FILE* cmd_pipe = popen("pidof -s r5apex.exe", "r");
        fgets(buf, 512, cmd_pipe);
        pid_t pid = strtoul(buf, NULL, 10);
        pclose(cmd_pipe);
        m_pid = pid;
        return pid;
    }
#endif
    bool IsValidPointer(long Pointer) {
        return Pointer > 0x00010000 && Pointer < 0x7FFFFFFEFFFF;
    }
    bool Read(long address, void* pBuff, size_t size) {
        if (size == 0)
            return false;

#ifdef _WIN32
        if (m_process == NULL) {
            GetPID(); // Try to get process handle
            if (m_process == NULL) return false;
        }

        SIZE_T bytesRead;
        BOOL result = ReadProcessMemory(m_process, (LPCVOID)address, pBuff, size, &bytesRead);
        return result && (bytesRead == size);
#else
        void* pAddress = (void*)address;
        pid_t pid = GetPID();
        struct iovec iovLocalAddressSpace[1]{ 0 };
        struct iovec iovRemoteAddressSpace[1]{ 0 };
        iovLocalAddressSpace[0].iov_base = pBuff;     // Store data in this buffer
        iovLocalAddressSpace[0].iov_len = size;       // which has this size.
        iovRemoteAddressSpace[0].iov_base = pAddress; // The data comes from here
        iovRemoteAddressSpace[0].iov_len = size;      // and has this size.
        ssize_t sSize = process_vm_readv(
            pid,                   // Remote process id
            iovLocalAddressSpace,  // Local iovec array
            1,                     // Size of the local iovec array
            iovRemoteAddressSpace, // Remote iovec array
            1,                     // Size of the remote iovec array
            0);                    // Flags, unused
        if (sSize == (ssize_t)size)
            return true;
        else if (sSize == 0)
            return false;
        return false;
#endif
    }
    bool Write(long address, void* pBuff, size_t size) {
        if (size == 0)
            return false;

#ifdef _WIN32
        if (m_process == NULL) {
            GetPID(); // Try to get process handle
            if (m_process == NULL) return false;
        }

        SIZE_T bytesWritten;
        BOOL result = WriteProcessMemory(m_process, (LPVOID)address, pBuff, size, &bytesWritten);
        return result && (bytesWritten == size);
#else
        void* pAddress = (void*)address;
        pid_t pid = GetPID();
        struct iovec iovLocalAddressSpace[1]{ 0 };
        struct iovec iovRemoteAddressSpace[1]{ 0 };
        iovLocalAddressSpace[0].iov_base = pBuff;     // Store data in this buffer
        iovLocalAddressSpace[0].iov_len = size;       // which has this size.
        iovRemoteAddressSpace[0].iov_base = pAddress; // The data will be writted here
        iovRemoteAddressSpace[0].iov_len = size;      // and has this size.
        ssize_t sSize = process_vm_writev(
            pid,                   // Remote process id
            iovLocalAddressSpace,  // Local iovec array
            1,                     // Size of the local iovec array
            iovRemoteAddressSpace, // Remote iovec array
            1,                     // Size of the remote iovec array
            0);                    // Flags, unused
        if (sSize == (ssize_t)size)
            return true;
        else if (sSize == 0)
            return false;
        return false;
#endif
    }
    std::string convertPointerToHexString(long pointer) {
        std::stringstream stream;
        stream << "0x" << std::hex << pointer;
        std::string result(stream.str());
        return result;
    }
    template <class T>
    void Write(long address, T value) {
        bool success = Write(address, &value, sizeof(T));
        if (!success) {
            m_pid = 0;
            throw std::invalid_argument(
                "Failed to write memory " + std::to_string(sizeof(T)) + " at: " + convertPointerToHexString(address));
        }
    }
    template <class T>
    T Read(long address, std::string stringRead) {
        T buffer;
        bool success = Read(address, &buffer, sizeof(T));
        if (!success) {
            m_pid = 0;
            throw std::invalid_argument("Failed to read memory [" + stringRead + "] at address : " + convertPointerToHexString(address));
        }
        return buffer;
    }
    void readbytearray(long address, char* buffer, int size) {
    	for (int i = 0; i < size; i++) {
    	    bool success = Read((long)(address + (long)i), &(buffer[i]), sizeof(char));
            if (!success)
                throw new std::invalid_argument("Failed to read byte at address: " + address);
    	}
    }
    std::string ReadString(long address, int size, std::string whatAreYouReading) {
        char buffer[size] = { 0 };
        bool success = Read(address, &buffer, size);
        if (!success) {
            m_pid = 0;
            throw std::invalid_argument("Failed to read memory string [" + whatAreYouReading + "] at address : " + convertPointerToHexString(address));
        }
        return std::string(buffer);
    }

#ifdef _WIN32
    void Cleanup() {
        if (m_process != NULL) {
            CloseHandle(m_process);
            m_process = NULL;
        }
        m_pid = 0;
    }
#endif
}
