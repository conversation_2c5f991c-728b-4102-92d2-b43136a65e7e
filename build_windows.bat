@echo off
echo Building Nika for Windows...

REM Change to script directory
cd /d "%~dp0"

REM Check if CMakeLists.txt exists
if not exist "CMakeLists.txt" (
    echo Error: CMakeLists.txt not found in current directory!
    echo Make sure you're running this script from the project root directory.
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Create build directory
if not exist "build" mkdir build
cd build

REM Configure with CMake
echo Configuring with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% NEQ 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build the project
echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo Run: .\Release\main.exe
pause
