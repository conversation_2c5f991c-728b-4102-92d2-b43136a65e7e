@echo off
title Nika One-Click Setup

echo ========================================
echo    Nika One-Click Setup for Windows
echo ========================================
echo.
echo This script will:
echo 1. Install vcpkg package manager
echo 2. Install GLFW3 and OpenGL dependencies
echo 3. Configure and build the project
echo.
echo Make sure you have:
echo - Visual Studio 2022 with C++ tools
echo - Git
echo - CMake
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

REM Change to script directory
cd /d "%~dp0"

echo.
echo ========================================
echo    Step 1: Installing Dependencies
echo ========================================
call install_dependencies.bat
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Step 2: Building Project
echo ========================================
call simple_build.bat
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to build project!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Setup Complete!
echo ========================================
echo.
echo Nika has been successfully built!
echo.
echo To run Nika:
echo 1. Start Apex Legends
echo 2. Run as Administrator: .\build\Release\main.exe
echo.
echo Controls:
echo - Left Arrow: Toggle aimbot left lock (shows "<")
echo - Right Arrow: Toggle ADS locking (shows ">")
echo - Up Arrow: Toggle triggerbot auto fire (shows "^")
echo.
echo Enjoy! Remember to use responsibly.
echo.
pause
