@echo off
title Nika Build (Manual GLFW)

echo ========================================
echo    Nika Build with Manual GLFW3
echo ========================================
echo.

REM Get the directory where this script is located
set SCRIPT_DIR=%~dp0
echo Script location: %SCRIPT_DIR%

REM Change to the script directory
cd /d "%SCRIPT_DIR%"
echo Current directory: %CD%
echo.

REM Check for required files
echo Checking for required files...
if not exist "CMakeLists.txt" (
    echo [ERROR] CMakeLists.txt not found!
    echo Please make sure you're in the correct project directory.
    pause
    exit /b 1
)

if not exist "Nika.cpp" (
    echo [ERROR] Nika.cpp not found!
    pause
    exit /b 1
)

echo [OK] Required files found!

REM Check for GLFW3
echo Checking for GLFW3...
if not exist "dependencies\glfw" (
    echo [ERROR] GLFW3 not found in dependencies\glfw!
    echo Please run: install_glfw_manual.bat first
    pause
    exit /b 1
)

echo [OK] GLFW3 found in dependencies\glfw!
echo.

REM Check for CMake
echo Checking for CMake...
cmake --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] CMake not found!
    echo Please install CMake and add it to your PATH.
    pause
    exit /b 1
)
echo [OK] CMake found!
echo.

REM Clean previous build
if exist "build" (
    echo Cleaning previous build...
    rmdir /s /q "build"
)

REM Create build directory
echo Creating build directory...
mkdir build
cd build

REM Set up CMake arguments with manual GLFW3 path
set CMAKE_ARGS=-G "Visual Studio 17 2022" -A x64
set CMAKE_ARGS=%CMAKE_ARGS% -DCMAKE_PREFIX_PATH="%CD%\..\dependencies\glfw"

REM Check if GLFW3 has CMake config
if exist "..\dependencies\glfw\lib\cmake\glfw3" (
    echo [INFO] Using GLFW3 CMake config...
    set CMAKE_ARGS=%CMAKE_ARGS% -Dglfw3_DIR="%CD%\..\dependencies\glfw\lib\cmake\glfw3"
) else if exist "..\dependencies\glfw\build\src\Release" (
    echo [INFO] Using built GLFW3...
    set CMAKE_ARGS=%CMAKE_ARGS% -DGLFW3_LIBRARY="%CD%\..\dependencies\glfw\build\src\Release\glfw3.lib"
    set CMAKE_ARGS=%CMAKE_ARGS% -DGLFW3_INCLUDE_DIR="%CD%\..\dependencies\glfw\include"
) else if exist "..\dependencies\glfw\lib-vc2022" (
    echo [INFO] Using pre-compiled GLFW3...
    set CMAKE_ARGS=%CMAKE_ARGS% -DGLFW3_LIBRARY="%CD%\..\dependencies\glfw\lib-vc2022\glfw3.lib"
    set CMAKE_ARGS=%CMAKE_ARGS% -DGLFW3_INCLUDE_DIR="%CD%\..\dependencies\glfw\include"
)

REM Configure project
echo.
echo ========================================
echo    Configuring Project
echo ========================================
echo CMake args: %CMAKE_ARGS%
echo.
cmake .. %CMAKE_ARGS%

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [ERROR] CMake configuration failed!
    echo.
    echo Possible solutions:
    echo 1. Run: install_glfw_manual.bat
    echo 2. Check that Visual Studio 2022 is installed
    echo 3. Make sure CMake is in PATH
    echo.
    pause
    exit /b 1
)

REM Build project
echo.
echo ========================================
echo    Building Project
echo ========================================
cmake --build . --config Release

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [ERROR] Build failed!
    echo Check the error messages above for details.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Build Successful!
echo ========================================
echo.
echo Executable location: %CD%\Release\main.exe
echo.
echo To run Nika:
echo 1. Start Apex Legends
echo 2. Run as Administrator: .\Release\main.exe
echo.
echo Controls:
echo - Left Arrow: Toggle aimbot left lock
echo - Right Arrow: Toggle ADS locking
echo - Up Arrow: Toggle triggerbot auto fire
echo.
pause
