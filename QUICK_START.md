# Quick Start Guide

## Windows 11 (Recommended for beginners)

### Automated Setup
```powershell
# Run as Administrator
.\setup_windows.ps1
```

### Manual Setup
1. Install Visual Studio 2022 with C++ tools
2. Run: `.\build_windows.bat` or `.\build_windows.ps1`
3. Start Apex Legends
4. Run as Administrator: `.\build\Release\main.exe`

### Generate Visual Studio Solution
```cmd
.\generate_vs_solution.bat
```

## Linux

### Quick Build
```bash
mkdir build && cd build
cmake .. && make
sudo ./main
```

### With Dependencies (Ubuntu/Debian)
```bash
sudo apt-get install -y libudev-dev cmake xorg-dev libglu1-mesa-dev libxrandr-dev libxinerama-dev libxcursor-dev libxi-dev libx11-dev libxtst-dev build-essential

# Install GLFW
git clone https://github.com/glfw/glfw.git
cd glfw && mkdir build && cd build
cmake .. && make && sudo make install
cd ../..

# Build Nika
mkdir build && cd build
cmake .. && make
sudo ./main
```

## Controls

- **Left Arrow**: Toggle aimbot left lock (shows "<" symbol)
- **Right Arrow**: Toggle ADS locking (shows ">" symbol)  
- **Up Arrow**: Toggle triggerbot auto fire (shows "^" symbol)
- **Hold Shift**: Lock on target + auto fire

## Troubleshooting

### Windows
- Run as Administrator
- Make sure Apex Legends is running
- Check Windows Defender/Antivirus isn't blocking

### Linux  
- Run with sudo
- Make sure Apex Legends is running
- Set game to Borderless Window mode

## Project Structure

- `Nika.cpp` - Main application
- `Memory.hpp` - Cross-platform memory operations
- `MyDisplay.hpp` - Cross-platform input simulation
- `Utils/Overlay.hpp` - GLFW overlay system
- `includes.hpp` - Platform-specific headers
- `CMakeLists.txt` - Cross-platform build configuration
