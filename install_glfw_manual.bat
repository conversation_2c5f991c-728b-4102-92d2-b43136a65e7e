@echo off
title Install GLFW3 Manually

echo ========================================
echo    Manual GLFW3 Installation
echo ========================================
echo.

REM Change to script directory
cd /d "%~dp0"

echo This script will download and install GLFW3 manually.
echo.
echo Options:
echo 1. Download pre-compiled GLFW3 binaries (Recommended)
echo 2. Build GLFW3 from source
echo 3. Exit
echo.
set /p choice="Choose option (1-3): "

if "%choice%"=="1" goto :download_precompiled
if "%choice%"=="2" goto :build_from_source
if "%choice%"=="3" goto :exit
echo Invalid choice!
pause
exit /b 1

:download_precompiled
echo.
echo ========================================
echo    Downloading Pre-compiled GLFW3
echo ========================================

REM Check if curl is available
curl --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] curl not found!
    echo Please download GLFW3 manually from: https://www.glfw.org/download.html
    echo Download the "64-bit Windows binaries" and extract to a "glfw" folder
    pause
    exit /b 1
)

REM Create dependencies directory
if not exist "dependencies" mkdir dependencies
cd dependencies

REM Download GLFW3 (latest version)
echo Downloading GLFW3...
curl -L -o glfw-3.3.8.bin.WIN64.zip https://github.com/glfw/glfw/releases/download/3.3.8/glfw-3.3.8.bin.WIN64.zip

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to download GLFW3!
    echo Please download manually from: https://www.glfw.org/download.html
    pause
    exit /b 1
)

REM Extract GLFW3
echo Extracting GLFW3...
powershell -command "Expand-Archive -Path 'glfw-3.3.8.bin.WIN64.zip' -DestinationPath '.' -Force"

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to extract GLFW3!
    echo Please extract manually.
    pause
    exit /b 1
)

REM Rename folder for easier access
if exist "glfw-3.3.8.bin.WIN64" (
    if exist "glfw" rmdir /s /q "glfw"
    ren "glfw-3.3.8.bin.WIN64" "glfw"
)

echo [SUCCESS] GLFW3 downloaded and extracted!
goto :setup_cmake

:build_from_source
echo.
echo ========================================
echo    Building GLFW3 from Source
echo ========================================

REM Check for git
git --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Git not found!
    echo Please install Git from: https://git-scm.com/download/win
    pause
    exit /b 1
)

REM Check for CMake
cmake --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] CMake not found!
    echo Please install CMake from: https://cmake.org/download/
    pause
    exit /b 1
)

REM Create dependencies directory
if not exist "dependencies" mkdir dependencies
cd dependencies

REM Clone GLFW3
if exist "glfw" (
    echo GLFW directory exists, removing...
    rmdir /s /q "glfw"
)

echo Cloning GLFW3...
git clone https://github.com/glfw/glfw.git
cd glfw

REM Build GLFW3
echo Building GLFW3...
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to build GLFW3!
    pause
    exit /b 1
)

cd ..\..
echo [SUCCESS] GLFW3 built from source!

:setup_cmake
echo.
echo ========================================
echo    Setting up CMake Configuration
echo ========================================

cd ..

REM Create a CMake configuration file
echo Creating CMake configuration...
(
echo # GLFW3 Configuration
echo set^(GLFW3_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/glfw"^)
echo set^(glfw3_DIR "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/glfw/lib/cmake/glfw3"^)
echo set^(CMAKE_PREFIX_PATH "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/glfw" ${CMAKE_PREFIX_PATH}^)
) > glfw_config.cmake

echo.
echo ========================================
echo    Installation Complete!
echo ========================================
echo.
echo GLFW3 has been installed to: dependencies\glfw
echo.
echo Now you can build the project using:
echo   simple_build_manual.bat
echo.
pause
goto :exit

:exit
exit /b 0
