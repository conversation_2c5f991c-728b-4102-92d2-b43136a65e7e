#include <cmath>
#include <algorithm>
#include <limits>
#include <cstdint>
#include <sstream>
#include <math.h>
#include <cstring>
#include <iostream>
#include <string>
#include <unistd.h>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <thread>
#include <map>
#include <array>
#include <X11/Xlib.h>
#include <X11/Xutil.h>
#include <X11/extensions/XTest.h>
#include <random>
#include <fstream>
#include <sys/stat.h>
#include <sys/uio.h>
#include <cctype>
#include <locale>
#include <iterator>
#include <atomic>
//==========[CORE]=============
#include "Vectors.hpp"
#include "Memory.hpp"
#include "Offsets.hpp"
#include "QAngle.hpp"
#include "Resolver.hpp"
//==========[SETTINGS]==============
#include "MyDisplay.hpp"
#include "ConfigLoader.hpp"
#include "Weapons.hpp"
//==========[UTILS]=============
#include "Structs.hpp"
//==========[FEATURES]=============
#include "Aim.hpp"
#include "NoRecoil.hpp"
#include "Random.hpp"
#include "Sense.hpp"
#include "TriggerBot.hpp"

