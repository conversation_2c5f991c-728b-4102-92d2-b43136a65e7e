cmake_minimum_required(VERSION 3.10)

project(main VERSION 0.1)

# Disable warnings
add_definitions(-w)

# Set C++ standard to C++23
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED True)

# Platform detection
if(WIN32)
    add_definitions(-D_WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
endif()

# Add the source files for ImGui
add_library(imgui STATIC
  imgui/imgui.cpp
  imgui/imgui_widgets.cpp
  imgui/imgui_impl_opengl3.cpp
  imgui/imgui_impl_glfw.cpp
  imgui/imgui_draw.cpp
  imgui/imgui_tables.cpp
)

# Set the include directories for ImGui
target_include_directories(imgui PUBLIC
  ${CMAKE_CURRENT_SOURCE_DIR}
)

# Platform-specific packages and libraries
if(WIN32)
    # Windows-specific libraries
    find_package(OpenGL REQUIRED)

    # Try to find GLFW3 in multiple ways
    find_package(glfw3 QUIET)
    if(NOT glfw3_FOUND)
        find_package(glfw QUIET)
        if(NOT glfw_FOUND)
            # Try to find manually installed GLFW3
            if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/glfw")
                message(STATUS "Found manual GLFW3 installation")
                set(GLFW3_INCLUDE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/glfw/include")

                # Try different library locations
                if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/glfw/lib-vc2022/glfw3.lib")
                    set(GLFW3_LIBRARY "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/glfw/lib-vc2022/glfw3.lib")
                elseif(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/glfw/build/src/Release/glfw3.lib")
                    set(GLFW3_LIBRARY "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/glfw/build/src/Release/glfw3.lib")
                else()
                    message(FATAL_ERROR "GLFW3 library not found in dependencies/glfw")
                endif()

                # Create imported target
                add_library(glfw STATIC IMPORTED)
                set_target_properties(glfw PROPERTIES
                    IMPORTED_LOCATION "${GLFW3_LIBRARY}"
                    INTERFACE_INCLUDE_DIRECTORIES "${GLFW3_INCLUDE_DIR}"
                )
                set(glfw_FOUND TRUE)
            else()
                message(FATAL_ERROR
                    "GLFW3 not found! Please install GLFW3 using one of these methods:\n"
                    "1. Run: install_glfw_manual.bat\n"
                    "2. vcpkg: vcpkg install glfw3:x64-windows\n"
                    "3. Download from: https://www.glfw.org/download.html\n"
                    "4. Build from source: https://github.com/glfw/glfw\n"
                    "\nAfter installation, make sure GLFW3 is in your CMAKE_PREFIX_PATH")
            endif()
        endif()
    endif()
else()
    # Linux-specific packages
    find_package (X11 REQUIRED)
    # Add additional libs
    link_directories(/usr/local/lib)
endif()

# Add the executable target
add_executable(main Nika.cpp)

# Platform-specific linking
if(WIN32)
    # Windows libraries - handle both glfw3 and glfw targets
    if(TARGET glfw)
        set(GLFW_TARGET glfw)
    elseif(TARGET glfw3)
        set(GLFW_TARGET glfw3)
    else()
        message(FATAL_ERROR "Neither glfw nor glfw3 target found!")
    endif()

    target_link_libraries(main
        imgui
        ${GLFW_TARGET}
        OpenGL::GL
        user32
        gdi32
        shell32
        kernel32
        psapi
    )
else()
    # Linux libraries
    target_link_libraries(main
        imgui
        glfw3
        GL
        X11
        Xinerama
        Xi
        udev
        ${X11_LIBRARIES}
        ${X11_XTest_LIB}
    )
endif()

# Set the include directories for the executable
if(WIN32)
    target_include_directories(main PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/Utils
        ${CMAKE_CURRENT_SOURCE_DIR}/Core
        ${CMAKE_CURRENT_SOURCE_DIR}/Math
        ${CMAKE_CURRENT_SOURCE_DIR}/Features
        ${CMAKE_CURRENT_SOURCE_DIR}/Overlay
    )
else()
    target_include_directories(main PUBLIC
        /usr/local/include
        ${CMAKE_CURRENT_SOURCE_DIR}/Utils
        ${CMAKE_CURRENT_SOURCE_DIR}/Core
        ${CMAKE_CURRENT_SOURCE_DIR}/Math
        ${CMAKE_CURRENT_SOURCE_DIR}/Features
        ${CMAKE_CURRENT_SOURCE_DIR}/Overlay
    )
endif()

# Copy nika.ini to build folder
file(COPY ${CMAKE_SOURCE_DIR}/nika.ini DESTINATION ${CMAKE_BINARY_DIR})
