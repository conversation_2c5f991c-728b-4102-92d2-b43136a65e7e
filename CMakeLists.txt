cmake_minimum_required(VERSION 3.9)

project(main VERSION 0.1)

# Disable warnings
add_definitions(-w)

# Set C++ standard to C++23
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED True)

# Platform detection
if(WIN32)
    add_definitions(-D_WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
endif()

# Add the source files for ImGui
add_library(imgui STATIC
  imgui/imgui.cpp
  imgui/imgui_widgets.cpp
  imgui/imgui_impl_opengl3.cpp
  imgui/imgui_impl_glfw.cpp
  imgui/imgui_draw.cpp
  imgui/imgui_tables.cpp
)

# Set the include directories for ImGui
target_include_directories(imgui PUBLIC
  ${CMAKE_CURRENT_SOURCE_DIR}
)

# Platform-specific packages and libraries
if(WIN32)
    # Windows-specific libraries
    find_package(OpenGL REQUIRED)
    find_package(glfw3 REQUIRED)
else()
    # Linux-specific packages
    find_package (X11 REQUIRED)
    # Add additional libs
    link_directories(/usr/local/lib)
endif()

# Add the executable target
add_executable(main Nika.cpp)

# Platform-specific linking
if(WIN32)
    # Windows libraries
    target_link_libraries(main
        imgui
        glfw
        OpenGL::GL
        user32
        gdi32
        shell32
        kernel32
        psapi
    )
else()
    # Linux libraries
    target_link_libraries(main
        imgui
        glfw3
        GL
        X11
        Xinerama
        Xi
        udev
        ${X11_LIBRARIES}
        ${X11_XTest_LIB}
    )
endif()

# Set the include directories for the executable
if(WIN32)
    target_include_directories(main PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/Utils
        ${CMAKE_CURRENT_SOURCE_DIR}/Core
        ${CMAKE_CURRENT_SOURCE_DIR}/Math
        ${CMAKE_CURRENT_SOURCE_DIR}/Features
        ${CMAKE_CURRENT_SOURCE_DIR}/Overlay
    )
else()
    target_include_directories(main PUBLIC
        /usr/local/include
        ${CMAKE_CURRENT_SOURCE_DIR}/Utils
        ${CMAKE_CURRENT_SOURCE_DIR}/Core
        ${CMAKE_CURRENT_SOURCE_DIR}/Math
        ${CMAKE_CURRENT_SOURCE_DIR}/Features
        ${CMAKE_CURRENT_SOURCE_DIR}/Overlay
    )
endif()

# Copy nika.ini to build folder
file(COPY ${CMAKE_SOURCE_DIR}/nika.ini DESTINATION ${CMAKE_BINARY_DIR})
