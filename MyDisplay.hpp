#pragma once
struct MyDisplay {
private:
#ifdef _WIN32
    // Windows doesn't need a display handle like X11
#else
    Display* display = XOpenDisplay(NULL);
#endif

public:
    MyDisplay() {
#ifdef _WIN32
        // Windows initialization if needed
#else
        display = XOpenDisplay(NULL);
        if (!display) throw std::invalid_argument("Could not open display");
#endif
    }

    void mouseClickLeft() {
#ifdef _WIN32
        INPUT input = {0};
        input.type = INPUT_MOUSE;
        input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
        SendInput(1, &input, sizeof(INPUT));

        input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
        SendInput(1, &input, sizeof(INPUT));
#else
        XTestFakeButtonEvent(display, Button1, True, 0);
        XTestFakeButtonEvent(display, Button1, False, 0);
        XFlush(display);
#endif
    }
    void mouseClick(int button)
    {
#ifdef _WIN32
        INPUT input = {0};
        input.type = INPUT_MOUSE;

        // Map button numbers to Windows flags
        DWORD downFlag, upFlag;
        switch(button) {
            case 1: // Left button
                downFlag = MOUSEEVENTF_LEFTDOWN;
                upFlag = MOUSEEVENTF_LEFTUP;
                break;
            case 2: // Middle button
                downFlag = MOUSEEVENTF_MIDDLEDOWN;
                upFlag = MOUSEEVENTF_MIDDLEUP;
                break;
            case 3: // Right button
                downFlag = MOUSEEVENTF_RIGHTDOWN;
                upFlag = MOUSEEVENTF_RIGHTUP;
                break;
            default:
                return;
        }

        input.mi.dwFlags = downFlag;
        SendInput(1, &input, sizeof(INPUT));
        Sleep(100);
        input.mi.dwFlags = upFlag;
        SendInput(1, &input, sizeof(INPUT));
#else
        XEvent event;
        memset(&event, 0x00, sizeof(event));
        event.type = ButtonPress;
        event.xbutton.button = button;
        event.xbutton.same_screen = True;
        XQueryPointer(display, RootWindow(display, DefaultScreen(display)), &event.xbutton.root, &event.xbutton.window, &event.xbutton.x_root, &event.xbutton.y_root, &event.xbutton.x, &event.xbutton.y, &event.xbutton.state);
        event.xbutton.subwindow = event.xbutton.window;
        while (event.xbutton.subwindow)
        {
            event.xbutton.window = event.xbutton.subwindow;
            XQueryPointer(display, event.xbutton.window, &event.xbutton.root, &event.xbutton.subwindow, &event.xbutton.x_root, &event.xbutton.y_root, &event.xbutton.x, &event.xbutton.y, &event.xbutton.state);
        }
        if (XSendEvent(display, PointerWindow, True, 0xfff, &event) == 0)
            fprintf(stderr, "Error\n");
        XFlush(display);
        usleep(100000);
        event.type = ButtonRelease;
        event.xbutton.state = 0x100;
        if (XSendEvent(display, PointerWindow, True, 0xfff, &event) == 0)
            fprintf(stderr, "Error\n");
        XFlush(display);
#endif
    }
    void moveMouseRelative(int pitchMovement, int yawMovement) {
#ifdef _WIN32
        INPUT input = {0};
        input.type = INPUT_MOUSE;
        input.mi.dwFlags = MOUSEEVENTF_MOVE;
        input.mi.dx = yawMovement;
        input.mi.dy = pitchMovement;
        SendInput(1, &input, sizeof(INPUT));
#else
        XTestFakeRelativeMotionEvent(display, yawMovement, pitchMovement, CurrentTime);
        XFlush(display);
#endif
    }
    bool isLeftMouseButtonDown() {
#ifdef _WIN32
        return (GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0;
#else
        Window root, child;
        int root_x, root_y, win_x, win_y;
        unsigned int mask;
        if (XQueryPointer(display, XRootWindow(display, DefaultScreen(display)), &root, &child, &root_x, &root_y, &win_x, &win_y, &mask))
            return (mask & Button1Mask) != 0;
        return false;
#endif
    }
//_begin
    bool isRightMouseButtonDown() {
#ifdef _WIN32
        return (GetAsyncKeyState(VK_RBUTTON) & 0x8000) != 0;
#else
        Window root, child;
        int root_x, root_y, win_x, win_y;
        unsigned int mask;
        if (XQueryPointer(display, XRootWindow(display, DefaultScreen(display)), &root, &child, &root_x, &root_y, &win_x, &win_y, &mask))
            return (mask & Button2Mask) != 0;
        return false;
#endif
    }
//_end
    bool keyDown(int keyCode) {
#ifdef _WIN32
        return (GetAsyncKeyState(keyCode) & 0x8000) != 0;
#else
        char keys_return[32];
        XQueryKeymap(display, keys_return);
        KeyCode kc2 = XKeysymToKeycode(display, keyCode);
        bool buttonDown = !!(keys_return[kc2 >> 3] & (1 << (kc2 & 7)));
        return buttonDown;
#endif
    }
    bool keyDown(std::string XK_keyName) {
#ifdef _WIN32
        // Convert X11 key names to Windows virtual key codes
        std::string keyName = trimXKPrefix(XK_keyName);
        int vkCode = 0;

        if (keyName == "Left") vkCode = VK_LEFT;
        else if (keyName == "Right") vkCode = VK_RIGHT;
        else if (keyName == "Up") vkCode = VK_UP;
        else if (keyName == "Down") vkCode = VK_DOWN;
        else if (keyName == "P" || keyName == "p") vkCode = 'P';
        else if (keyName == "M" || keyName == "m") vkCode = 'M';
        else if (keyName.length() == 1) {
            char c = keyName[0];
            if (c >= 'a' && c <= 'z') vkCode = c - 'a' + 'A';
            else if (c >= 'A' && c <= 'Z') vkCode = c;
            else if (c >= '0' && c <= '9') vkCode = c;
        }

        return vkCode != 0 ? keyDown(vkCode) : false;
#else
        KeySym keyCode = XStringToKeysym(trimXKPrefix(XK_keyName).c_str());
        return keyDown(keyCode);
#endif
    }
    std::string trimXKPrefix(const std::string& keyName) {
        if (keyName.compare(0, 3, "XK_") == 0)
            return keyName.substr(3);
        return keyName;
    }
};
