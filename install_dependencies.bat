@echo off
title Install Nika Dependencies

echo ========================================
echo    Installing Nika Dependencies
echo ========================================
echo.

REM Change to script directory
cd /d "%~dp0"

REM Check if vcpkg already exists
if exist "vcpkg" (
    echo vcpkg directory already exists.
    echo Do you want to reinstall? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo Removing existing vcpkg...
        rmdir /s /q "vcpkg"
    ) else (
        goto :install_deps
    )
)

echo.
echo Step 1: Installing vcpkg...
echo ========================================

REM Check for git
git --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Git not found!
    echo Please install Git from: https://git-scm.com/download/win
    pause
    exit /b 1
)

REM Clone vcpkg
echo Cloning vcpkg...
git clone https://github.com/Microsoft/vcpkg.git
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to clone vcpkg!
    pause
    exit /b 1
)

cd vcpkg

REM Bootstrap vcpkg
echo.
echo Bootstrapping vcpkg...
call bootstrap-vcpkg.bat
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to bootstrap vcpkg!
    pause
    exit /b 1
)

cd ..

:install_deps
echo.
echo Step 2: Installing dependencies...
echo ========================================

cd vcpkg

REM Install GLFW3
echo Installing GLFW3...
vcpkg install glfw3:x64-windows
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to install GLFW3!
    pause
    exit /b 1
)

REM Install OpenGL (usually comes with system)
echo Installing OpenGL...
vcpkg install opengl:x64-windows
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] OpenGL installation failed, but this is usually OK as it comes with graphics drivers.
)

REM Integrate with Visual Studio
echo.
echo Step 3: Integrating with Visual Studio...
echo ========================================
vcpkg integrate install
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to integrate with Visual Studio!
    pause
    exit /b 1
)

cd ..

echo.
echo ========================================
echo    Dependencies Installed Successfully!
echo ========================================
echo.
echo Installed packages:
echo - GLFW3 (for window management and input)
echo - OpenGL (for graphics rendering)
echo.
echo vcpkg has been integrated with Visual Studio.
echo You can now build the project using:
echo   simple_build.bat
echo   OR
echo   build_windows.bat
echo.
echo Note: Make sure to run the build scripts as Administrator!
echo.
pause
